"""
Base Agent - Foundation class for all trading agents
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable
from enum import Enum

from models.model_instance import OllamaModelInstance
from .agent_memory import AgentMemory
from .agent_communication import AgentCommunication
from .reasoning_engine import ReasoningEngine

logger = logging.getLogger(__name__)


class AgentState(Enum):
    """Agent lifecycle states"""
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    BUSY = "busy"
    IDLE = "idle"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"


class AgentRole(Enum):
    """Agent role types"""
    TEAM_LEADER = "team_leader"
    MARKET_ANALYST = "market_analyst"
    STRATEGY_DEVELOPER = "strategy_developer"
    RISK_MANAGER = "risk_manager"
    EXECUTION_SPECIALIST = "execution_specialist"
    PERFORMANCE_EVALUATOR = "performance_evaluator"


class BaseAgent(ABC):
    """
    Base class for all trading agents.
    Provides core functionality including communication, memory, reasoning, and lifecycle management.
    """
    
    def __init__(self, 
                 agent_id: str = None,
                 name: str = None,
                 role: AgentRole = None,
                 config: Dict[str, Any] = None):
        
        # Identity
        self.agent_id = agent_id or str(uuid.uuid4())
        self.name = name or f"Agent_{self.agent_id[:8]}"
        self.role = role
        self.config = config or {}
        
        # State management
        self.state = AgentState.CREATED
        self.created_at = time.time()
        self.last_activity = time.time()
        
        # Core components
        self.model_instance: Optional[OllamaModelInstance] = None
        self.memory: Optional[AgentMemory] = None
        self.communication: Optional[AgentCommunication] = None
        self.reasoning_engine: Optional[ReasoningEngine] = None
        
        # Performance tracking
        self.metrics = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'avg_response_time': 0.0,
            'total_response_time': 0.0,
            'last_task_time': None
        }
        
        # Task management
        self.current_task: Optional[Dict[str, Any]] = None
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.task_handlers: Dict[str, Callable] = {}
        
        # Lifecycle management
        self.running = False
        self.main_task: Optional[asyncio.Task] = None
        
    async def initialize(self, 
                        model_instance: OllamaModelInstance,
                        message_broker = None) -> bool:
        """Initialize the agent with required components"""
        try:
            logger.info(f"Initializing agent {self.name} ({self.role.value if self.role else 'unknown'})")
            self.state = AgentState.INITIALIZING
            
            # Set model instance
            self.model_instance = model_instance
            
            # Initialize memory
            self.memory = AgentMemory(
                agent_id=self.agent_id,
                config=self.config.get('memory', {})
            )
            await self.memory.initialize()
            
            # Initialize communication
            self.communication = AgentCommunication(
                agent_id=self.agent_id,
                agent_name=self.name,
                message_broker=message_broker,
                config=self.config.get('communication', {})
            )
            await self.communication.initialize()
            
            # Initialize reasoning engine
            self.reasoning_engine = ReasoningEngine(
                agent_id=self.agent_id,
                model_instance=self.model_instance,
                memory=self.memory,
                config=self.config.get('reasoning', {})
            )
            await self.reasoning_engine.initialize()
            
            # Register task handlers
            self._register_task_handlers()
            
            # Agent-specific initialization
            await self._initialize_agent()
            
            self.state = AgentState.IDLE
            logger.info(f"✓ Agent {self.name} initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize agent {self.name}: {e}")
            self.state = AgentState.ERROR
            return False
            
    async def start(self):
        """Start the agent's main processing loop"""
        if self.running:
            return
            
        logger.info(f"Starting agent {self.name}")
        self.running = True
        self.state = AgentState.ACTIVE
        
        # Start main processing task
        self.main_task = asyncio.create_task(self._main_loop())
        
        # Start communication
        await self.communication.start()
        
        logger.info(f"✓ Agent {self.name} started")
        
    async def stop(self):
        """Stop the agent gracefully"""
        if not self.running:
            return
            
        logger.info(f"Stopping agent {self.name}")
        self.state = AgentState.STOPPING
        self.running = False
        
        # Cancel main task
        if self.main_task and not self.main_task.done():
            self.main_task.cancel()
            try:
                await self.main_task
            except asyncio.CancelledError:
                pass
                
        # Stop components
        if self.communication:
            await self.communication.stop()
            
        self.state = AgentState.STOPPED
        logger.info(f"✓ Agent {self.name} stopped")
        
    async def _main_loop(self):
        """Main processing loop for the agent"""
        while self.running:
            try:
                # Process tasks from queue
                try:
                    task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                    await self._process_task(task)
                except asyncio.TimeoutError:
                    # No task available, perform idle activities
                    await self._idle_activities()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in main loop for agent {self.name}: {e}")
                await asyncio.sleep(1)
                
    async def _process_task(self, task: Dict[str, Any]):
        """Process a single task"""
        task_start_time = time.time()
        self.current_task = task
        self.state = AgentState.BUSY
        
        try:
            task_type = task.get('type')
            task_handler = self.task_handlers.get(task_type)
            
            if task_handler:
                result = await task_handler(task)
                
                # Update metrics
                self.metrics['completed_tasks'] += 1
                response_time = time.time() - task_start_time
                self.metrics['total_response_time'] += response_time
                self.metrics['avg_response_time'] = (
                    self.metrics['total_response_time'] / self.metrics['completed_tasks']
                )
                
                # Send result if callback specified
                callback = task.get('callback')
                if callback:
                    await callback(result)
                    
            else:
                logger.warning(f"No handler for task type: {task_type}")
                self.metrics['failed_tasks'] += 1
                
        except Exception as e:
            logger.error(f"Error processing task for agent {self.name}: {e}")
            self.metrics['failed_tasks'] += 1
            
        finally:
            self.current_task = None
            self.state = AgentState.IDLE
            self.last_activity = time.time()
            self.metrics['total_tasks'] += 1
            self.metrics['last_task_time'] = time.time()
            
    def _register_task_handlers(self):
        """Register task handlers for different task types"""
        self.task_handlers = {
            'analyze': self._handle_analyze_task,
            'decide': self._handle_decide_task,
            'communicate': self._handle_communicate_task,
            'learn': self._handle_learn_task,
            'status': self._handle_status_task
        }
        
        # Add role-specific handlers
        self._register_role_specific_handlers()
        
    @abstractmethod
    def _register_role_specific_handlers(self):
        """Register role-specific task handlers (implemented by subclasses)"""
        pass
        
    @abstractmethod
    async def _initialize_agent(self):
        """Agent-specific initialization (implemented by subclasses)"""
        pass
        
    @abstractmethod
    async def _idle_activities(self):
        """Activities to perform when idle (implemented by subclasses)"""
        pass
        
    async def _handle_analyze_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle analysis tasks"""
        data = task.get('data', {})
        analysis_type = task.get('analysis_type', 'general')
        
        # Use reasoning engine to analyze
        result = await self.reasoning_engine.analyze(data, analysis_type)
        
        # Store in memory
        await self.memory.store_analysis(result)
        
        return result
        
    async def _handle_decide_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle decision tasks"""
        options = task.get('options', [])
        context = task.get('context', {})
        
        # Use reasoning engine to make decision
        decision = await self.reasoning_engine.decide(options, context)
        
        # Store decision in memory
        await self.memory.store_decision(decision)
        
        return decision
        
    async def _handle_communicate_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle communication tasks"""
        message = task.get('message')
        recipient = task.get('recipient')
        
        if message and recipient:
            await self.communication.send_message(recipient, message)
            return {'status': 'sent', 'recipient': recipient}
        else:
            return {'status': 'error', 'error': 'Missing message or recipient'}
            
    async def _handle_learn_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle learning tasks"""
        experience = task.get('experience', {})
        
        # Store experience in memory
        await self.memory.store_experience(experience)
        
        # Update reasoning patterns
        await self.reasoning_engine.learn_from_experience(experience)
        
        return {'status': 'learned', 'experience_id': experience.get('id')}
        
    async def _handle_status_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle status request tasks"""
        return await self.get_status()
        
    async def submit_task(self, task: Dict[str, Any]):
        """Submit a task to the agent's queue"""
        await self.task_queue.put(task)
        
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            'agent_id': self.agent_id,
            'name': self.name,
            'role': self.role.value if self.role else None,
            'state': self.state.value,
            'created_at': self.created_at,
            'last_activity': self.last_activity,
            'current_task': self.current_task,
            'queue_size': self.task_queue.qsize(),
            'metrics': self.metrics.copy(),
            'model': self.model_instance.model_name if self.model_instance else None
        }
        
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get detailed performance metrics"""
        base_metrics = self.metrics.copy()
        
        # Add model performance if available
        if self.model_instance:
            model_metrics = self.model_instance.get_performance_metrics()
            base_metrics['model_metrics'] = model_metrics
            
        # Add memory metrics
        if self.memory:
            memory_metrics = await self.memory.get_metrics()
            base_metrics['memory_metrics'] = memory_metrics
            
        return base_metrics
        
    def __str__(self) -> str:
        return f"Agent({self.name}:{self.role.value if self.role else 'unknown'}:{self.state.value})"
        
    def __repr__(self) -> str:
        return self.__str__()
