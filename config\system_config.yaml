# Advanced Ollama Trading Agent System Configuration

# System Information
system:
  name: "Advanced Ollama Trading Agent System"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true
  log_level: "INFO"

# Ollama Configuration
ollama:
  base_url: "http://localhost:11434"
  timeout: 300
  max_retries: 3
  retry_delay: 1.0
  model_cache_size: 5  # Number of models to keep in memory
  
# Database Configuration
databases:
  postgres:
    host: "localhost"
    port: 5432
    database: "trading_agents"
    username: "trading_user"
    password: "trading_password"
    pool_size: 20
    max_overflow: 30
    
  redis:
    host: "localhost"
    port: 6379
    database: 0
    password: null
    max_connections: 100
    
  clickhouse:
    host: "localhost"
    port: 9000
    database: "analytics"
    username: "default"
    password: ""

# Agent Configuration
agents:
  max_agents: 50
  heartbeat_interval: 30  # seconds
  response_timeout: 60    # seconds
  memory_limit: "2GB"
  
  # Default agent settings
  defaults:
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0

# Team Configuration
teams:
  max_teams: 10
  team_formation_interval: 300  # seconds (5 minutes)
  team_evaluation_interval: 900  # seconds (15 minutes)
  min_team_size: 3
  max_team_size: 8
  
  # Team types and their configurations
  team_types:
    aggressive_growth:
      risk_tolerance: 0.8
      return_target: 0.15
      max_drawdown: 0.20
      
    defensive:
      risk_tolerance: 0.3
      return_target: 0.08
      max_drawdown: 0.10
      
    range_trading:
      risk_tolerance: 0.5
      return_target: 0.12
      max_drawdown: 0.15
      
    volatility_exploitation:
      risk_tolerance: 0.9
      return_target: 0.20
      max_drawdown: 0.25
      
    carry_strategy:
      risk_tolerance: 0.4
      return_target: 0.10
      max_drawdown: 0.12

# Market Data Configuration
market_data:
  providers:
    - name: "yfinance"
      enabled: true
      priority: 1
    - name: "ccxt"
      enabled: true
      priority: 2
      
  update_intervals:
    real_time: 1      # seconds
    minute: 60        # seconds
    hourly: 3600      # seconds
    daily: 86400      # seconds
    
  symbols:
    stocks:
      - "AAPL"
      - "GOOGL"
      - "MSFT"
      - "TSLA"
      - "NVDA"
    crypto:
      - "BTC/USD"
      - "ETH/USD"
      - "SOL/USD"
    forex:
      - "EUR/USD"
      - "GBP/USD"
      - "USD/JPY"

# Risk Management
risk_management:
  global_limits:
    max_portfolio_risk: 0.02  # 2% of portfolio per trade
    max_daily_loss: 0.05      # 5% daily loss limit
    max_drawdown: 0.15        # 15% maximum drawdown
    
  position_sizing:
    method: "kelly_criterion"  # kelly_criterion, fixed_fractional, volatility_adjusted
    base_size: 0.01           # 1% of portfolio
    max_size: 0.05            # 5% maximum position size
    
  stop_loss:
    default_percentage: 0.02   # 2% stop loss
    trailing_stop: true
    
# Performance Monitoring
monitoring:
  metrics_collection_interval: 60  # seconds
  performance_evaluation_interval: 3600  # seconds (1 hour)
  
  dashboards:
    enabled: true
    port: 8080
    refresh_interval: 30  # seconds
    
  alerts:
    enabled: true
    channels:
      - "console"
      - "file"
      # - "email"
      # - "slack"
      
  thresholds:
    high_latency: 5.0      # seconds
    low_performance: -0.05  # -5% performance threshold
    high_drawdown: 0.10     # 10% drawdown alert

# Learning and Adaptation
learning:
  enabled: true
  learning_rate: 0.001
  batch_size: 32
  evaluation_frequency: 1000  # iterations
  
  strategies:
    reinforcement_learning: true
    supervised_learning: true
    transfer_learning: true
    meta_learning: true
    
  repository:
    max_strategies: 1000
    cleanup_interval: 86400  # seconds (daily)
    backup_interval: 604800  # seconds (weekly)

# Security Configuration
security:
  encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_interval: 2592000  # seconds (monthly)
    
  authentication:
    enabled: true
    token_expiry: 3600  # seconds (1 hour)
    max_login_attempts: 5
    
  audit:
    enabled: true
    log_all_decisions: true
    retention_period: 2592000  # seconds (30 days)

# Communication
communication:
  protocols:
    - "redis_pubsub"
    - "websocket"
    
  message_queue:
    max_queue_size: 10000
    message_ttl: 300  # seconds
    
  coordination:
    consensus_timeout: 30  # seconds
    voting_period: 60      # seconds
    quorum_percentage: 0.6 # 60% for decisions

# Execution
execution:
  enabled: true
  paper_trading: true  # Set to false for live trading
  
  brokers:
    - name: "paper_broker"
      enabled: true
      type: "simulation"
      
  order_management:
    max_orders_per_second: 10
    order_timeout: 300  # seconds
    
# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  handlers:
    console:
      enabled: true
      level: "INFO"
      
    file:
      enabled: true
      level: "DEBUG"
      filename: "logs/trading_system.log"
      max_size: "100MB"
      backup_count: 5
      
    structured:
      enabled: true
      format: "json"
      filename: "logs/structured.log"
