#!/usr/bin/env python3
"""
Advanced Ollama Trading Agent System
Main entry point for the trading system
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

import click
import yaml
from rich.console import Console
from rich.logging import RichHandler

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from teams.team_manager import TeamManager
from communication.message_broker import MessageBroker
from monitoring.system_monitor import SystemMonitor
from data.market_data_manager import MarketDataManager

console = Console()
logger = logging.getLogger(__name__)


class TradingSystem:
    """Main trading system orchestrator"""
    
    def __init__(self, config_path: str = "config/system_config.yaml"):
        self.config_path = config_path
        self.config = None
        self.running = False
        
        # Core components
        self.ollama_hub: Optional[OllamaModelHub] = None
        self.agent_manager: Optional[AgentManager] = None
        self.team_manager: Optional[TeamManager] = None
        self.message_broker: Optional[MessageBroker] = None
        self.system_monitor: Optional[SystemMonitor] = None
        self.market_data_manager: Optional[MarketDataManager] = None
        
    async def load_config(self):
        """Load system configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
            
    def setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO'))
        
        # Setup rich handler for console output
        logging.basicConfig(
            level=level,
            format=log_config.get('format', '%(message)s'),
            datefmt="[%X]",
            handlers=[RichHandler(console=console, rich_tracebacks=True)]
        )
        
        # Setup file handler if enabled
        file_config = log_config.get('handlers', {}).get('file', {})
        if file_config.get('enabled', False):
            file_handler = logging.FileHandler(
                file_config.get('filename', 'logs/trading_system.log')
            )
            file_handler.setLevel(getattr(logging, file_config.get('level', 'DEBUG')))
            file_handler.setFormatter(
                logging.Formatter(log_config.get('format'))
            )
            logging.getLogger().addHandler(file_handler)
            
    async def initialize_components(self):
        """Initialize all system components"""
        logger.info("Initializing system components...")
        
        try:
            # Initialize Ollama Model Hub
            self.ollama_hub = OllamaModelHub(
                base_url=self.config['ollama']['base_url'],
                config=self.config
            )
            await self.ollama_hub.initialize()
            logger.info("✓ Ollama Model Hub initialized")
            
            # Initialize Message Broker
            self.message_broker = MessageBroker(self.config)
            await self.message_broker.initialize()
            logger.info("✓ Message Broker initialized")
            
            # Initialize Market Data Manager
            self.market_data_manager = MarketDataManager(self.config)
            await self.market_data_manager.initialize()
            logger.info("✓ Market Data Manager initialized")
            
            # Initialize Agent Manager
            self.agent_manager = AgentManager(
                ollama_hub=self.ollama_hub,
                message_broker=self.message_broker,
                config=self.config
            )
            await self.agent_manager.initialize()
            logger.info("✓ Agent Manager initialized")
            
            # Initialize Team Manager
            self.team_manager = TeamManager(
                agent_manager=self.agent_manager,
                message_broker=self.message_broker,
                market_data_manager=self.market_data_manager,
                config=self.config
            )
            await self.team_manager.initialize()
            logger.info("✓ Team Manager initialized")
            
            # Initialize System Monitor
            self.system_monitor = SystemMonitor(
                agent_manager=self.agent_manager,
                team_manager=self.team_manager,
                ollama_hub=self.ollama_hub,
                config=self.config
            )
            await self.system_monitor.initialize()
            logger.info("✓ System Monitor initialized")
            
            logger.info("🚀 All components initialized successfully!")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
            
    async def start_system(self):
        """Start the trading system"""
        logger.info("Starting Advanced Ollama Trading Agent System...")
        
        try:
            # Start all components
            await asyncio.gather(
                self.ollama_hub.start(),
                self.message_broker.start(),
                self.market_data_manager.start(),
                self.agent_manager.start(),
                self.team_manager.start(),
                self.system_monitor.start()
            )
            
            self.running = True
            logger.info("🎯 Trading system started successfully!")
            
            # Display system status
            await self.display_system_status()
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            raise
            
    async def stop_system(self):
        """Stop the trading system gracefully"""
        if not self.running:
            return
            
        logger.info("Stopping trading system...")
        self.running = False
        
        try:
            # Stop all components
            stop_tasks = []
            if self.system_monitor:
                stop_tasks.append(self.system_monitor.stop())
            if self.team_manager:
                stop_tasks.append(self.team_manager.stop())
            if self.agent_manager:
                stop_tasks.append(self.agent_manager.stop())
            if self.market_data_manager:
                stop_tasks.append(self.market_data_manager.stop())
            if self.message_broker:
                stop_tasks.append(self.message_broker.stop())
            if self.ollama_hub:
                stop_tasks.append(self.ollama_hub.stop())
                
            await asyncio.gather(*stop_tasks, return_exceptions=True)
            logger.info("✓ Trading system stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during system shutdown: {e}")
            
    async def display_system_status(self):
        """Display current system status"""
        console.print("\n" + "="*60)
        console.print("🤖 ADVANCED OLLAMA TRADING AGENT SYSTEM", style="bold blue")
        console.print("="*60)
        
        if self.ollama_hub:
            models = await self.ollama_hub.get_available_models()
            console.print(f"📊 Available Models: {len(models)}")
            
        if self.agent_manager:
            agents = await self.agent_manager.get_active_agents()
            console.print(f"🤖 Active Agents: {len(agents)}")
            
        if self.team_manager:
            teams = await self.team_manager.get_active_teams()
            console.print(f"👥 Active Teams: {len(teams)}")
            
        console.print("="*60)
        console.print("System is running. Press Ctrl+C to stop.", style="green")
        console.print("="*60 + "\n")
        
    async def run(self):
        """Main run loop"""
        try:
            await self.load_config()
            self.setup_logging()
            await self.initialize_components()
            await self.start_system()
            
            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error(f"System error: {e}")
            raise
        finally:
            await self.stop_system()


def signal_handler(signum, frame):
    """Handle system signals"""
    logger.info(f"Received signal {signum}")
    # The main loop will handle the actual shutdown


@click.command()
@click.option('--config', '-c', default='config/system_config.yaml',
              help='Path to configuration file')
@click.option('--debug', '-d', is_flag=True, help='Enable debug mode')
def main(config: str, debug: bool):
    """Advanced Ollama Trading Agent System"""
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and run the trading system
    system = TradingSystem(config_path=config)
    
    try:
        asyncio.run(system.run())
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="yellow")
    except Exception as e:
        console.print(f"\n❌ System error: {e}", style="red")
        sys.exit(1)


if __name__ == "__main__":
    main()
