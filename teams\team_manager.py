"""
Team Manager - Advanced team coordination and management system
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any
from enum import Enum
import json

from agents.base_agent import Agent<PERSON><PERSON>
from communication.message_types import MessageType, create_analysis_request
from .team_formation_engine import TeamFormationEngine
from .hierarchical_structures import HierarchicalTeamStructure
from .decision_protocols import DecisionProtocols
from .collaboration_framework import CollaborationFramework
from .team_performance_evaluation import TeamPerformanceEvaluation

logger = logging.getLogger(__name__)


class TeamType(Enum):
    """Types of trading teams"""
    MOMENTUM_TEAM = "momentum_team"
    MEAN_REVERSION_TEAM = "mean_reversion_team"
    ARBITRAGE_TEAM = "arbitrage_team"
    RISK_MANAGEMENT_TEAM = "risk_management_team"
    RESEARCH_TEAM = "research_team"


class TeamStatus(Enum):
    """Team status states"""
    FORMING = "forming"
    ACTIVE = "active"
    PAUSED = "paused"
    DISSOLVING = "dissolving"
    DISSOLVED = "dissolved"


class TeamManager:
    """
    Advanced team coordination and management system.
    Handles dynamic team formation, coordination, and performance optimization.
    """

    def __init__(self, agent_manager, message_broker, market_data_manager, config: Dict[str, Any]):
        self.agent_manager = agent_manager
        self.message_broker = message_broker
        self.market_data_manager = market_data_manager
        self.config = config

        # Team management state
        self.teams: Dict[str, Dict[str, Any]] = {}
        self.team_templates: Dict[TeamType, Dict[str, Any]] = {}
        self.team_performance: Dict[str, Dict[str, Any]] = {}

        # Coordination state
        self.active_missions: Dict[str, Dict[str, Any]] = {}
        self.team_communications: Dict[str, List[Dict[str, Any]]] = {}

        # Advanced team components
        self.team_formation_engine: Optional[TeamFormationEngine] = None
        self.hierarchical_structure: Optional[HierarchicalTeamStructure] = None
        self.decision_protocols: Optional[DecisionProtocols] = None
        self.collaboration_framework: Optional[CollaborationFramework] = None
        self.performance_evaluation: Optional[TeamPerformanceEvaluation] = None

        # State
        self.initialized = False
        self.running = False

    async def initialize(self):
        """Initialize the team manager"""
        if self.initialized:
            return

        logger.info("Initializing Advanced Team Manager...")

        # Setup team templates
        await self._setup_team_templates()

        # Initialize coordination protocols
        await self._setup_coordination_protocols()

        # Initialize advanced team components
        await self._initialize_advanced_components()

        self.initialized = True
        logger.info("✓ Advanced Team Manager initialized")

    async def _setup_team_templates(self):
        """Setup team templates for different trading strategies"""
        self.team_templates = {
            TeamType.MOMENTUM_TEAM: {
                'required_roles': [
                    AgentRole.TEAM_LEADER,
                    AgentRole.MARKET_ANALYST,
                    AgentRole.STRATEGY_DEVELOPER,
                    AgentRole.EXECUTION_SPECIALIST
                ],
                'optional_roles': [AgentRole.RISK_MANAGER],
                'coordination_style': 'fast_execution',
                'decision_threshold': 0.7,
                'max_team_size': 5
            },
            TeamType.MEAN_REVERSION_TEAM: {
                'required_roles': [
                    AgentRole.TEAM_LEADER,
                    AgentRole.MARKET_ANALYST,
                    AgentRole.RISK_MANAGER,
                    AgentRole.EXECUTION_SPECIALIST
                ],
                'optional_roles': [AgentRole.STRATEGY_DEVELOPER],
                'coordination_style': 'careful_analysis',
                'decision_threshold': 0.8,
                'max_team_size': 4
            },
            TeamType.ARBITRAGE_TEAM: {
                'required_roles': [
                    AgentRole.MARKET_ANALYST,
                    AgentRole.EXECUTION_SPECIALIST,
                    AgentRole.RISK_MANAGER
                ],
                'optional_roles': [AgentRole.TEAM_LEADER],
                'coordination_style': 'ultra_fast',
                'decision_threshold': 0.9,
                'max_team_size': 3
            }
        }

    async def _setup_coordination_protocols(self):
        """Setup team coordination protocols"""
        self.coordination_protocols = {
            'fast_execution': {
                'decision_timeout': 30,  # seconds
                'consensus_required': False,
                'parallel_processing': True
            },
            'careful_analysis': {
                'decision_timeout': 300,  # 5 minutes
                'consensus_required': True,
                'parallel_processing': False
            },
            'ultra_fast': {
                'decision_timeout': 5,  # 5 seconds
                'consensus_required': False,
                'parallel_processing': True
            }
        }

    async def _initialize_advanced_components(self):
        """Initialize advanced team management components"""
        # Initialize Team Formation Engine
        self.team_formation_engine = TeamFormationEngine(self.agent_manager, self.config)
        await self.team_formation_engine.initialize()

        # Initialize Hierarchical Structure
        self.hierarchical_structure = HierarchicalTeamStructure(self.config)
        await self.hierarchical_structure.initialize()

        # Initialize Decision Protocols
        self.decision_protocols = DecisionProtocols(self.hierarchical_structure, self.config)
        await self.decision_protocols.initialize()

        # Initialize Collaboration Framework
        self.collaboration_framework = CollaborationFramework(
            self, self.decision_protocols, self.config
        )
        await self.collaboration_framework.initialize()

        # Initialize Performance Evaluation
        self.performance_evaluation = TeamPerformanceEvaluation(
            self.collaboration_framework, self.decision_protocols,
            self.hierarchical_structure, self.config
        )
        await self.performance_evaluation.initialize()

        logger.info("✓ Advanced team components initialized")

    async def start(self):
        """Start the team manager"""
        if not self.initialized:
            await self.initialize()

        if self.running:
            return

        logger.info("Starting Advanced Team Manager...")
        self.running = True
        logger.info("✓ Advanced Team Manager started")

    async def stop(self):
        """Stop the team manager"""
        if not self.running:
            return

        logger.info("Stopping Advanced Team Manager...")
        self.running = False

        # Dissolve all active teams
        for team_id in list(self.teams.keys()):
            await self.dissolve_team(team_id)

        logger.info("✓ Advanced Team Manager stopped")

    async def create_team(self, team_type: TeamType, mission: Dict[str, Any]) -> Optional[str]:
        """Create a new trading team"""
        try:
            team_id = f"team_{team_type.value}_{int(time.time())}"
            template = self.team_templates.get(team_type)

            if not template:
                logger.error(f"No template found for team type: {team_type}")
                return None

            # Get available agents
            available_agents = await self._get_available_agents(template['required_roles'])

            if len(available_agents) < len(template['required_roles']):
                logger.warning(f"Insufficient agents for team {team_type}")
                return None

            # Create team
            team = {
                'team_id': team_id,
                'team_type': team_type,
                'status': TeamStatus.FORMING,
                'members': available_agents,
                'mission': mission,
                'template': template,
                'created_at': time.time(),
                'performance_metrics': {}
            }

            self.teams[team_id] = team

            # Notify team members
            await self._notify_team_formation(team_id, team)

            # Activate team
            await self._activate_team(team_id)

            logger.info(f"✓ Created team {team_id} of type {team_type.value}")
            return team_id

        except Exception as e:
            logger.error(f"Error creating team: {e}")
            return None

    async def _get_available_agents(self, required_roles: List[AgentRole]) -> List[str]:
        """Get available agents for team formation"""
        available_agents = []

        for role in required_roles:
            agents = await self.agent_manager.get_agents_by_role(role)
            if agents:
                # Get the first available agent of this role
                available_agents.append(agents[0].agent_id)

        return available_agents

    async def _notify_team_formation(self, team_id: str, team: Dict[str, Any]):
        """Notify agents about team formation"""
        for agent_id in team['members']:
            # Send team formation notification
            # This would use the message broker to notify agents
            pass

    async def _activate_team(self, team_id: str):
        """Activate a team"""
        if team_id not in self.teams:
            return

        team = self.teams[team_id]
        team['status'] = TeamStatus.ACTIVE
        team['activated_at'] = time.time()

        logger.info(f"✓ Activated team {team_id}")

    async def dissolve_team(self, team_id: str) -> bool:
        """Dissolve a team"""
        if team_id not in self.teams:
            return False

        team = self.teams[team_id]
        team['status'] = TeamStatus.DISSOLVING

        # Notify team members
        await self._notify_team_dissolution(team_id, team)

        # Clean up team resources
        team['status'] = TeamStatus.DISSOLVED
        team['dissolved_at'] = time.time()

        logger.info(f"✓ Dissolved team {team_id}")
        return True

    async def _notify_team_dissolution(self, team_id: str, team: Dict[str, Any]):
        """Notify agents about team dissolution"""
        for agent_id in team['members']:
            # Send team dissolution notification
            pass

    async def coordinate_team_mission(self, team_id: str, mission_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate a team mission"""
        if team_id not in self.teams:
            return {'success': False, 'error': 'Team not found'}

        team = self.teams[team_id]
        mission_id = f"mission_{team_id}_{int(time.time())}"

        # Create mission
        mission = {
            'mission_id': mission_id,
            'team_id': team_id,
            'mission_data': mission_data,
            'status': 'active',
            'started_at': time.time(),
            'coordination_style': team['template']['coordination_style']
        }

        self.active_missions[mission_id] = mission

        # Coordinate based on team style
        result = await self._execute_coordination_style(mission)

        return result

    async def _execute_coordination_style(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Execute mission based on coordination style"""
        style = mission['coordination_style']
        protocol = self.coordination_protocols.get(style, {})

        if style == 'ultra_fast':
            return await self._ultra_fast_coordination(mission)
        elif style == 'fast_execution':
            return await self._fast_execution_coordination(mission)
        elif style == 'careful_analysis':
            return await self._careful_analysis_coordination(mission)
        else:
            return {'success': False, 'error': 'Unknown coordination style'}

    async def _ultra_fast_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Ultra-fast coordination for arbitrage teams"""
        # Implement ultra-fast coordination logic
        return {'success': True, 'coordination_type': 'ultra_fast', 'execution_time': 0.1}

    async def _fast_execution_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Fast execution coordination for momentum teams"""
        # Implement fast execution coordination logic
        return {'success': True, 'coordination_type': 'fast_execution', 'execution_time': 2.0}

    async def _careful_analysis_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Careful analysis coordination for mean reversion teams"""
        # Implement careful analysis coordination logic
        return {'success': True, 'coordination_type': 'careful_analysis', 'execution_time': 30.0}

    async def get_active_teams(self) -> List[Dict[str, Any]]:
        """Get all active teams"""
        active_teams = []
        for team_id, team in self.teams.items():
            if team['status'] == TeamStatus.ACTIVE:
                active_teams.append(team)
        return active_teams

    async def get_team_performance(self, team_id: str) -> Dict[str, Any]:
        """Get team performance metrics"""
        if team_id not in self.teams:
            return {}

        return self.team_performance.get(team_id, {})

    async def optimize_team_composition(self, team_id: str) -> Dict[str, Any]:
        """Optimize team composition based on performance"""
        if team_id not in self.teams:
            return {'success': False, 'error': 'Team not found'}

        # Implement team optimization logic
        return {'success': True, 'optimization': 'team_composition_optimized'}
